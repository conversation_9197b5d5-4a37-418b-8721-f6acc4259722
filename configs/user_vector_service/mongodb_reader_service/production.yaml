# MongoDB用户数据读取微服务 - 生产环境配置
# 版本: 1.0.0

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "1.0.0"
  environment: "production"

# ==================== 服务配置 ====================
service:
  name: "mongodb_reader_service"
  port: 8003

# ==================== MongoDB配置 ====================
mongodb:
  # 连接配置
  connection:
    host: "************"
    port: 27017
    username: "nrdc_user"
    password: "Nr@#dc12Pwd"
    database: "nrdc"
    auth_source: "admin"
    
  # 连接池配置
  pool:
    min_pool_size: 10
    max_pool_size: 100
    max_idle_time_ms: 30000
    
  # 集合配置
  collection_pattern: "user_pid_records_optimized_{prov_id}"
  
  # 查询配置
  query:
    timeout_ms: 60000
    batch_size: 1000

# ==================== Redis配置 ====================
redis:
  host: "************"
  port: 6379
  db: 0
  password: "redis_password"
  
  # 队列配置
  vector_queue_name: "vector_processing_queue"
  
  # 队列长度控制配置
  queue_control:
    # 队列长度检查间隔（秒）
    check_interval: 3
    # 最大队列长度
    max_queue_size: 200
    # 暂停阈值：队列长度超过此值时暂停发送
    pause_threshold: 150
    # 恢复阈值：队列长度低于此值时恢复发送
    resume_threshold: 50

# ==================== 批处理配置 ====================
batch_processing:
  # 用户批次大小
  user_batch_size: 100000
  # 查询超时时间（秒）
  query_timeout: 60
  # 批次发送间隔（毫秒）
  batch_send_interval: 50

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/user_vector_service/mongodb_reader_service/mongodb_reader_service.log
  file_max_size: "100MB"
  file_backup_count: 10
  console_enabled: true
  console_colored: false
  structured: false

# ==================== 监控配置 ====================
monitoring:
  # 是否启用监控
  enabled: true
  # 统计报告间隔（秒）
  stats_report_interval: 60
  # 性能监控间隔（秒）
  performance_monitor_interval: 120
